"use client"

import React from 'react'
import { NestedThemeProvider, useTheme, useIsNestedTheme, useThemeDepth } from "@apollo/ui"

// Example theme configurations
const darkTheme = {
  'apl-colors-surface-static-default1': '#1a1a1a',
  'apl-colors-surface-static-default2': '#2a2a2a',
  'apl-colors-content-default': '#ffffff',
  'apl-colors-border-default': '#404040',
}

const primaryTheme = {
  'apl-colors-content-primary-default': '#0066cc',
  'apl-colors-border-primary-default': '#0066cc',
  'apl-colors-surface-action-primary-default': '#0066cc',
}

const warningTheme = {
  'apl-colors-content-warning-default': '#ff6600',
  'apl-colors-border-warning-default': '#ff6600',
  'apl-colors-surface-static-warning-default': '#fff3e0',
}

// Component to display theme information
function ThemeInfo({ title }: { title: string }) {
  const { theme, parentTheme, isNested, depth } = useTheme()
  const isNestedHook = useIsNestedTheme()
  const themeDepth = useThemeDepth()

  return (
    <div style={{
      padding: '16px',
      border: '1px solid var(--apl-colors-border-default)',
      backgroundColor: 'var(--apl-colors-surface-static-default1)',
      color: 'var(--apl-colors-content-default)',
      margin: '8px 0',
      borderRadius: '8px'
    }}>
      <h4 style={{ margin: '0 0 8px 0', color: 'var(--apl-colors-content-primary-default)' }}>
        {title}
      </h4>
      <div style={{ fontSize: '14px', lineHeight: '1.4' }}>
        <p>Is Nested: {isNested ? 'Yes' : 'No'}</p>
        <p>Depth: {depth}</p>
        <p>Hook Nested: {isNestedHook ? 'Yes' : 'No'}</p>
        <p>Hook Depth: {themeDepth}</p>
        <p>Theme Keys: {Object.keys(theme).length}</p>
        {parentTheme && <p>Parent Theme Keys: {Object.keys(parentTheme).length}</p>}
      </div>
    </div>
  )
}

// Themed card component
function ThemedCard({ children, title }: { children: React.ReactNode; title: string }) {
  return (
    <div style={{
      padding: '16px',
      backgroundColor: 'var(--apl-colors-surface-static-default2)',
      border: '1px solid var(--apl-colors-border-default)',
      borderRadius: '8px',
      color: 'var(--apl-colors-content-default)',
      margin: '8px 0'
    }}>
      <h3 style={{
        color: 'var(--apl-colors-content-primary-default)',
        marginTop: 0,
        marginBottom: '12px'
      }}>
        {title}
      </h3>
      {children}
    </div>
  )
}

export default function ThemePage() {
  return (
    <NestedThemeProvider>
      <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
        <h1 style={{ color: 'var(--apl-colors-content-default)' }}>
          NestedThemeProvider Examples
        </h1>

        {/* Root Theme Section */}
        <section style={{ marginBottom: '40px' }}>
          <h2 style={{ color: 'var(--apl-colors-content-primary-default)' }}>
            Root Theme (Apollo Base)
          </h2>
          <ThemeInfo title="Root Theme Info" />
          <ThemedCard title="Root Level Card">
            This card uses the base Apollo theme from apolloTokens. All CSS variables are available.
          </ThemedCard>
        </section>

        {/* Dark Theme Section */}
        <NestedThemeProvider theme={darkTheme} scope=".dark-section">
          <section style={{
            padding: '20px',
            backgroundColor: 'var(--apl-colors-surface-static-default1)',
            borderRadius: '12px',
            marginBottom: '40px'
          }}>
            <h2 style={{ color: 'var(--apl-colors-content-default)' }}>
              Dark Theme (Nested)
            </h2>
            <ThemeInfo title="Dark Theme Info" />
            <ThemedCard title="Dark Themed Card">
              This card inherits from the base theme but overrides with dark colors.
              Notice how the background and text colors have changed.
            </ThemedCard>

            {/* Double Nested - Primary Theme */}
            <NestedThemeProvider theme={primaryTheme} scope=".primary-section">
              <div style={{
                padding: '20px',
                backgroundColor: 'var(--apl-colors-surface-static-default2)',
                borderRadius: '8px',
                marginTop: '20px'
              }}>
                <h3 style={{ color: 'var(--apl-colors-content-primary-default)' }}>
                  Primary Theme (Double Nested)
                </h3>
                <ThemeInfo title="Primary Theme Info" />
                <ThemedCard title="Primary Themed Card">
                  This card inherits from dark theme and adds primary color overrides.
                  Notice the depth is now 2!
                </ThemedCard>
              </div>
            </NestedThemeProvider>
          </section>
        </NestedThemeProvider>

        {/* Non-inheriting Theme Section */}
        <NestedThemeProvider theme={warningTheme} inherit={false} scope=".warning-section">
          <section style={{
            padding: '20px',
            backgroundColor: 'var(--apl-colors-surface-static-warning-default)',
            borderRadius: '12px',
            marginBottom: '40px'
          }}>
            <h2 style={{ color: 'var(--apl-colors-content-warning-default)' }}>
              Warning Theme (Non-inheriting)
            </h2>
            <ThemeInfo title="Warning Theme Info" />
            <ThemedCard title="Warning Card">
              This theme does NOT inherit from parent - it merges with base theme only.
              Notice the depth is 1, not 2, because inherit=false.
            </ThemedCard>
          </section>
        </NestedThemeProvider>

        {/* Custom Wrapper Example */}
        <NestedThemeProvider
          theme={primaryTheme}
          WrapperComponent="section"
          themeClassName="custom-primary-section"
          style={{
            border: '2px solid var(--apl-colors-border-primary-default)',
            borderRadius: '12px',
            padding: '20px',
            marginBottom: '40px'
          }}
        >
          <h2 style={{ color: 'var(--apl-colors-content-primary-default)' }}>
            Custom Wrapper (Section Element)
          </h2>
          <ThemeInfo title="Custom Wrapper Info" />
          <ThemedCard title="Section Card">
            This content is wrapped in a section element with primary theme and custom styling.
          </ThemedCard>
        </NestedThemeProvider>

        {/* Conditional Content Example */}
        <section style={{ marginBottom: '40px' }}>
          <h2 style={{ color: 'var(--apl-colors-content-primary-default)' }}>
            Conditional Theme Hooks
          </h2>
          <ConditionalContent />

          <NestedThemeProvider theme={darkTheme}>
            <div style={{
              padding: '16px',
              backgroundColor: 'var(--apl-colors-surface-static-default1)',
              borderRadius: '8px',
              margin: '8px 0'
            }}>
              <ConditionalContent />

              <NestedThemeProvider theme={primaryTheme}>
                <div style={{
                  padding: '16px',
                  backgroundColor: 'var(--apl-colors-surface-static-default2)',
                  borderRadius: '8px',
                  margin: '8px 0'
                }}>
                  <ConditionalContent />
                </div>
              </NestedThemeProvider>
            </div>
          </NestedThemeProvider>
        </section>
      </div>
    </NestedThemeProvider>
  )
}

// Component that demonstrates conditional rendering based on theme state
function ConditionalContent() {
  const isNested = useIsNestedTheme()
  const depth = useThemeDepth()

  return (
    <div style={{
      padding: '12px',
      backgroundColor: 'var(--apl-colors-surface-static-default2)',
      border: '1px solid var(--apl-colors-border-default)',
      borderRadius: '6px',
      margin: '8px 0',
      color: 'var(--apl-colors-content-default)'
    }}>
      {isNested ? (
        <p>🎯 This content knows it's in a nested theme at depth {depth}</p>
      ) : (
        <p>🏠 This content is in the root theme (depth {depth})</p>
      )}
    </div>
  )
}
